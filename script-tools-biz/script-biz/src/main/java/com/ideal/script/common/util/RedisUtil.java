package com.ideal.script.common.util;

import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;


/**
 * Redis工具类
 *
 * <AUTHOR>
 */
public class RedisUtil {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtil.class);

    private final RedissonClient redissonClient;

    public RedisUtil(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 尝试获取给定锁的分布式锁
     * @param lockKey 锁的唯一标识
     * @return true表示成功获取到锁，false表示获取锁失败
     */
    @SuppressWarnings("unused")
    public boolean acquireLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock("taskLock:" + lockKey);
        boolean lockAcquired = false;

        try {
            lockAcquired = lock.tryLock(waitTime, leaseTime, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
        } finally {
            if (lockAcquired) {
                lock.unlock(); // 释放锁
            }
        }

        return lockAcquired;
    }

    /**
     * 释放分布式锁
     * @param lock 分布式锁对象
     */
    @SuppressWarnings("unused")
    public void releaseLock(RLock lock) {
        if (lock.isLocked()) {
            lock.unlock();
        }
    }

    /**
     * 设置任务已处理标志
     * @param key 任务的唯一标识
     */
    public void setProcessedFlag(String key) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set("processed");
    }


    /**
     * Stores data in Redis with the specified key.
     *
     * <AUTHOR>
     * @param key   the key used to identify the data in Redis
     * @param value the data to be stored in Redis
     */
    public void storeData(String key, String value) {
        // 设置过期时间为60天
        long expirationTimeInMinutes = 60;

        RBucket<String> bucket = redissonClient.getBucket(key);
        bucket.set(value, expirationTimeInMinutes, TimeUnit.DAYS);
        logger.info("Data stored in Redis with key runtimeId: runtimeId {} from {}: " , key,value);
    }

    public String retrieveData(String key) {
        RBucket<String> bucket = redissonClient.getBucket(key);
        String value = bucket.get();
        if (value != null) {
           logger.info("Retrieved data from Redis with key runtimeId: runtimeId {} from {}: " , key,value);
            return value;
        } else {
            logger.info("No data found in Redis for key  runtimeId: runtimeId {} from {}: " , key, null);
            return null;
        }
    }

}
