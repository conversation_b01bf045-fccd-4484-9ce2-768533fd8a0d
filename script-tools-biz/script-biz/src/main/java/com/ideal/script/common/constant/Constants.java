package com.ideal.script.common.constant;

import com.ideal.sc.constants.StrPool;
import com.ideal.script.common.constant.enums.Enums;

/**
 * 常量类
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class Constants {


    private Constants() {
        // 隐藏的私有构造函数
    }

    public static final String REPONSE_STATUS_SUSSCESS_CODE = "10000";
    public static final String REPONSE_STATUS_VALIDATA_CODE = "10605";
    public static final String REPONSE_STATUS_FAIL_CODE = "10602";


    /**
     * 接口名称类
     */
    public static final String ADAPTOR_SHELLCMD = "shellcmd";

    /**
     * 任务执行调用agent方法
     */
    public static final String RPC_METHOD = "IEAIAgent.executeAct";

    /**
     * 重试执行agent方法
     */
    public static final String RETRY_RPC_METHOD = "IEAIAgent.reExecuteAct";

    /**
     * 脚本任务执行最终状态集合，终止、完成，红色完成(做过终止agent实例处理)
     */
    public static final int[] SCRIPT_FINISH_SET = {
            Enums.TaskInstanceStatus.COMPLETED.getValue(),
            Enums.TaskInstanceStatus.TERMINATED.getValue(),
            Enums.TaskInstanceStatus.COMPLETED_RED.getValue()
    };


    public static final String ALGORITHM = "AES/GCM/NoPadding";

    public static final String LIST_SUCCESS = "list.success";
    public static final String SHENANDOAH = "Constants.SHENANDOAH:";

    public static final String SCRIPT_EXECUTE_LOCK = "script-execute-result-lock";

    public static final String SCRIPT_ERROR_LOCK = "script-error-result-lock";

    public static final String SCRIPT_SEND_RESULT_LOCK = "script-send-result-lock";

    public static final String SCRIPT_WORK_DIR = "scriptWorkDir";
    public static final String COMMAND = "command";
    public static final String EXPECT_TYPE = "expectType";



    public static final String SERVICES_TYPE = "servicesType";

    public static final String AGENT_PORT = "agentPort";


    public static final String IS_SHUT_DOWN = "isShutdown";


    public static final String PARAM_VALUE = "@@script@@service@@";

    /**
     * 脚本服务化配置文件前缀
     */
    public static final String SCRIPT_PROP_PREFIX="ideal.script";

    public static final String REDIS_SCRIPT_TEMP_ATTACHMENT = "script:temp:attachment";

    /**
     * 是否已经消费了agent结果标识
     */
    public static final String CHECK_CONSUMPTION_SCRIPT_RUNTIME = "script:consumption:taskruntime:";
    /**
     * 脚本服务化消费幂等判断队列标识，send队列
     */
    public static final String CHECK_CONSUMPTION_TOPIC_SEND = "sendRes_";
    /**
     * 脚本服务化消费幂等判断队列标识，error队列
     */
    public static final String CHECK_CONSUMPTION_TOPIC_ERROR = "errorRes_";
    /**
     * 脚本服务化消费幂等判断队列标识，执行结果队列
     */
    public static final String CHECK_CONSUMPTION_TOPIC_EXEC = "execRes_";


    /**
     * 脚本执行模式标识
     */
    public static final String DRIVER_MODE = "driveMode";
    /**
     * 任务信息dto标识
     */
    public static final String TASK_START_DTO = "taskStartDto";
    /**
     * 用户信息标识
     */
    public static final String USER = "user";
    /**
     * 脚本服务化任务申请填写的并发数
     */
    public static final String EACH_NUM = "eachNum";
    /**
     * 脚本服务化任务申请agent总数
     */
    public static final String TOTAL = "total";
    /**
     * 脚本服务化任务当前执行计数器的值
     */
    public static final String NOW_COUNT = "nowCount";
    /**
     * 脚本服务化任务执行 redis前缀
     */
    private static final String SCRIPT_TASK_EXEC_REDIS_PREFIX_KEY = "script:task:exec-";
    /**
     * 脚本服务化任务执行信息 redis hash key，使用字符串格式化 script:task:exec-{任务实例id}，花括号必须有，避免Redis Cluster模式下 CROSSSLOT错误
     */
    public static final String SCRIPT_TASK_EXEC_REDIS_KEY = SCRIPT_TASK_EXEC_REDIS_PREFIX_KEY+ StrPool.DELIM_START + "%s" + StrPool.DELIM_END;
    /**
     * 脚本服务化任务执行计数器 redis key，使用字符串格式化  script:task:exec-{任务实例id}-total
     */
    public static final String SCRIPT_TASK_EXEC_REDIS_COUNTER_KEY = SCRIPT_TASK_EXEC_REDIS_KEY + StrPool.DASHED + TOTAL;
    /**
     * 脚本服务化任务执行，队列模式下 存储ips的 redis key，使用字符串格式化  script:task:exec-queue-{任务实例id}
     */
    public static final String SCRIPT_TASK_EXEC_REDIS_QUEUE_KEY = SCRIPT_TASK_EXEC_REDIS_PREFIX_KEY + "queue" + StrPool.DASHED + "%s";
    /**
     * 脚本发布通过、切换版本版本推送的mq管道名
     */
    public static final String SCRIPT_DEFAULT_VERSION_CHANGE_CHANEL = "scriptDefaultVersionChange-out-0";
    /**
     * 作业中心-任务统计，任务开始推送mq管道名
     */
    public static final String SCRIPT_JOB_START_CHANEL = "scriptJobStart-out-0";
    /**
     * 作业中心-任务统计，任务结束推送mq管道名
     */
    public static final String SCRIPT_JOB_END_CHANEL = "scriptJobEnd-out-0";

    /**
     * 连接agent异常推送mq管道名
     */
    public static final String CRONTABS_ERROR_RESULT_DEV = "crontabsErrorResult-out-0";

    /**
     * 连接agent成功推送mq管道名
     */
    public static final String CRONTABS_SEND_RESULT_DEV = "crontabsSendResult-out-0";

    /**
     * agent执行返回结果推送mq管道名
     */
    public static final String CRONTABS_EXECUTE_RESULT_DEV = "crontabsExecuteResult-out-0";
    /**
     * 临时附件上传接口使用的分隔符
     */
    public static final String ATTACHMENT_SPLIT_FLAG = "@@@@";

    /**
     * 脚本执行时，redis中保存的数据的超时时间
     */
    public static final Long EXEC_TASK_TIMEOUT_DAY = 1L;

    /**
     * 投产介质导出，服务投产导入，脚本源文件文件夹名称
     */
    public static final String SCRIPT_FILE_DIR_NAME = "scriptFiles";

}
