package com.ideal.script.mapper;

import com.ideal.script.model.bean.*;
import com.ideal.script.model.entity.Exectime;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.InfoVersionText;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MyScriptMapper {
     /**
      * 查询
      * @param scriptInfoQueryBean  我的脚本实体类
      * @return     结果
      */
     List<MyScriptBean> selectMyScriptList(ScriptInfoQueryBean scriptInfoQueryBean);

     /**
      * 查询
      * @param versionUuid  脚本基础表uuid
      * @return     结果
      */
     List<String> getLastVersion(String versionUuid);

     /**
      * 查询
      * @param iid  id
      * @return     结果
      */
     List<DownloadScriptBean> getScriptListForDownload(Long[] iid);

     /**
      * 查询
      * @param serviceUuid    脚本uuid
      * @return     结果
      */
     List<ScriptVersionInfoBean> getScriptServiceVersionListForAllScript(String serviceUuid);

     /**
      * 查询
      * @param iid  id
      * @return     结果
      */
     List<InfoVersionText> getNewContentInfo(Long iid);

     /**
      * 更新
      * @param content   内容
      * @param oldId     脚本id
      * @return     结果
      */
     int updateContentInfo(String content,Long oldId);

     /**
      * 脚本执行次数
      * @param exectime 脚本执行次数统计对象
      */
     void insertExecTime(Exectime exectime);

     /**
      * 查询版本表无版本的脚本信息
      * @param uniqueUuid 脚本唯一uuid
      * @return String 版本表uuid
      */
     InfoVersion getScriptsWithoutVersions(String uniqueUuid);

     /**
      * 根据脚本信息查询返回中文名、脚本版本uuid、脚本uuid
      * @param myScriptBean  脚本信息myScriptBean
      * @return MyScriptBean
      */
     MyScriptBean getScriptNameAndUuid(MyScriptBean myScriptBean);

     /**
      * 根据uuid查询数据条数
      * @param srcScriptUuid 脚本uuid
      * @return 数据条数
      */
     Integer getExecTimeCountBySrcScriptUuid(String srcScriptUuid);

     /**
      * 根据脚本版本id查询双人复核id
      * @param scriptVersionIds 脚本id
      * @return 双人复核id
      */
     List<Long> getWorkitemIdByVersionIds(Long[] scriptVersionIds);

     /**
      * 通过投产id获取投产子数据信息
      * @param productId 投产id
      */
     List<ItsmChildrenBean> getScriptInfoByProductId(@Param("productId") Long productId);

     /**
      * 根据脚本uuid获取脚本信息
      * @param uniqueUuids uuid
      */
     List<MyScriptBean> getScriptInfoByUniqueUuid(@Param("uniqueUuids") List<String> uniqueUuids);
}
