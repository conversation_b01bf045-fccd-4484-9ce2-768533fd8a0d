package com.ideal.script.mapper;

import java.sql.Timestamp;
import java.util.List;

import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.entity.TaskRuntime;
import org.apache.ibatis.annotations.Param;

/**
 * agent运行实例Mapper接口
 *
 * <AUTHOR>
 */
public interface TaskRuntimeMapper {
    /**
     * 查询agent运行实例
     *
     * @param id agent运行实例主键
     * @return agent运行实例
     */
    TaskRuntime selectTaskRuntimeById(Long id);

    /**
     * 根据taskRuntimeId查询是否为定时、周期、普通任务
     * @param taskRuntimeId agent实例id
     * @return 任务类型标识
     */
    Integer selectSchedulerTagByTaskRunTimeId(Long taskRuntimeId);

    /**
     * 查询agent运行实例列表
     *
     * @param taskRuntime agent运行实例
     * @return agent运行实例集合
     */
    List<TaskRuntime> selectTaskRuntimeList(TaskRuntime taskRuntime);

    /**
     * 获取异常agent列表
     * @param taskRuntime agent实例
     * @return 异常runtime数据
     */
    List<TaskRuntime> selectErrorRuntimeList(TaskRuntime taskRuntime);

    /**
     * 新增agent运行实例
     *
     * @param taskRuntime agent运行实例
     * @return 结果
     */
    int insertTaskRuntime(TaskRuntime taskRuntime);

    /**
     * 修改agent运行实例
     *
     * @param taskRuntime agent运行实例
     * @return 结果
     */
    int updateTaskRuntime(TaskRuntime taskRuntime);

    /**
     * 删除agent运行实例
     *
     * @param id agent运行实例主键
     * @return 结果
     */
    int deleteTaskRuntimeById(Long id);

    /**
     * 批量删除agent运行实例
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskRuntimeByIds(@Param("ids")Long[] ids);

    /**
     * 更新agent运行实例状态
     *
     * @param status 状态
     * @param notInStates 不查询的任务状态
     * @param taskRuntimeId agent运行实例表主键
     */
    void updateTaskRuntimeState(@Param("status") int status, @Param("notInStates") List<Integer> notInStates, @Param("taskRuntimeId") Long taskRuntimeId, @Param("endTime") Timestamp endTime, @Param("elapsedTime") Long elapsedTime);

    TaskRuntime getTaskRuntimeStartTime(@Param("taskRuntimeId") Long taskRuntimeId);

    /**
     * 功能描述：根据 Agent 实例 ID 查询当前任务中除自身外其他 Agent 执行出现异常的情况
     * 确定当前任务中除自身外其他 Agent 是否出现了执行异常的情况
     *
     * @param id agent实例id
     * @return {@link Integer }
     * <AUTHOR>
     */
    Integer selectCountByTaskInstanceId(Long id);

    /**
     * 获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto);

    /**
     * 功能描述：根据agent实例主键查询运行中的agent数量
     *
     * @param id agent实例id
     * @return {@link Integer }
     * <AUTHOR>
     */
    Integer getRunningAgentInstanceCount(Long id);

    /**
     * 功能描述：根据agent实例主键获取绑定的agent信息
     *
     * @param taskRuntimeId agent实例id
     * @return {@link TaskRunTimeBindAgentBean }
     * <AUTHOR>
     */
    TaskRunTimeBindAgentBean getBindAgentForTaskRuntime(Long taskRuntimeId);

    /**
     * 根据任务id获取实例数据
     * @param scriptTaskId 任务id
     * @return 实例数据集合
     */
    List<TaskRuntime> selectTaskRunTimeByTaskId(Long scriptTaskId);

    /**
     * 根据agent地址、任务实例id  查询agent实例id集合
     * @param agentAddressList agent地址集合
     * @param taskInstanceId 任务实例id
     * @return agent实例id集合
     */
    List<Long> selectRuntimeIdsByAgentAddressAndTaskInstanceId(List<String> agentAddressList, Long taskInstanceId);

    /**
     * 根据任务实例id获取对应的agent实例数据
     * @param taskInstanceId 任务实例数据
     * @return agent实例对象集合
     */
    List<TaskRuntime> getTaskRuntimeByInstanceId(Long taskInstanceId);
}
