package com.ideal.script.common.util;

import com.ideal.script.exception.ScriptException;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.reflect.Method;
import java.util.List;

/**
 * 批量处理工具类
 *
 * <AUTHOR>
 */
@Component("scriptBatchDataUtil")
public class BatchDataUtil {
    private static final Logger log = LoggerFactory.getLogger(BatchDataUtil.class);

    /**
     * 批量执行条数
     */
    private static final int BATCH_SIZE = 1000;

    /**
     * 批量处理方法，用于执行指定Mapper的指定方法对列表进行批量操作。
     *
     * @param <T1>            Mapper接口类型
     * @param <T2>            实体类类型
     * @param mapperClass     Mapper接口的Class对象
     * @param listEntity      待处理的实体类列表
     * @param entityClass     实体类的Class对象
     * @param methodName      Mapper接口中执行批量操作的方法名
     * @param batchSqlSession 批量处理的SqlSession
     * @param mapper          Mapper接口的实例
     * @throws ScriptException 当批量执行失败时抛出自定义通知异常
     * <AUTHOR>
     */
    public <T1, T2> void batchData(Class<T1> mapperClass, List<T2> listEntity, Class<T2> entityClass, String methodName, SqlSession batchSqlSession, T1 mapper)
            throws ScriptException {
        int i = 1;
        try {
            Method mapperClassDeclaredMethod = mapperClass.getDeclaredMethod(methodName, entityClass);
            int size = listEntity.size();
            for (T2 entity : listEntity) {
                mapperClassDeclaredMethod.invoke(mapper, entity);
                if ((i % BATCH_SIZE == 0) || i == size) {
                    batchSqlSession.flushStatements();
                }
                i++;
            }
            // 非事务环境下强制commit，事务情况下该commit相当于无效
            batchSqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
        } catch (Exception e) {
            batchSqlSession.rollback();
            log.error("batchData exception:",e);
            throw new ScriptException("save.fail");
        }
    }
}
