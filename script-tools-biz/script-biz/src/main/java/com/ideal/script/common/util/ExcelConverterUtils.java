package com.ideal.script.common.util;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.ReadCellData;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;

import java.util.Collections;
import java.util.EnumMap;
import java.util.HashMap;
import java.util.Map;

public class ExcelConverterUtils implements Converter<Integer> {

    public enum ConversionType {
        /**
         * 导入导出字段枚举
         */
        CONFIRMSTATE;


        private static final Map<ConversionType, Map<Integer, String>> MAPPINGS = new EnumMap<>(ConversionType.class);


        static {
            Map<Integer, String> confirmStateMap = new HashMap<>();
            confirmStateMap.put(0, "未确认");
            confirmStateMap.put(1, "已确认");


            MAPPINGS.put(CONFIRMSTATE, confirmStateMap);
        }

        public Map<Integer, String> getMapping() {
            return Collections.unmodifiableMap(MAPPINGS.get(this));
        }
    }

    private final Map<Integer, String> intToString;
    private final Map<String, Integer> stringToInt;

    public ExcelConverterUtils(ConversionType type) {
        this.intToString = type.getMapping();
        this.stringToInt = new HashMap<>();
        for (Map.Entry<Integer, String> entry : intToString.entrySet()) {
            this.stringToInt.put(entry.getValue(), entry.getKey());
        }
    }

    @Override
    public Class<Integer> supportJavaTypeKey() {
        return Integer.class;
    }

    @Override
    public WriteCellData<Integer> convertToExcelData(Integer value,
                                                     ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        return new WriteCellData<>(intToString.getOrDefault(value, "未知任务"));
    }

    @Override
    public Integer convertToJavaData(ReadCellData<?> cellData,
                                     ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String stringValue = cellData.getStringValue();
        return stringToInt.get(stringValue);
    }

    /**
     * 静态内部类作为转换器
     */
    public static class ConfirmStateConverter extends ExcelConverterUtils {
        public ConfirmStateConverter() {
            super(ConversionType.CONFIRMSTATE);
        }
    }


}
