package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 脚本任务运行实例对象 ieai_script_task_instance
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class TaskInstance implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @IdGenerator
    @Min(value = 0)
    private Long id;
    /**
     * ieai_script_task表id
     */
    private Long scriptTaskId;
    /**
     * ieai_script_info_version表的isrc_script_uuid
     */
    private String srcScriptUuid;
    /**
     * 状态 10-运行20-完成  30-异常 60-终止
     */
    private Integer status;
    /**
     * 开始时间
     */
    private Timestamp startTime;
    /**
     * 结束时间
     */
    private Timestamp endTime;
    /**
     * 正在运行的设备数
     */
    private Integer runNums;
    /**
     * server数量
     */
    private Integer serverNum;
    /**
     * 是否定时任务 2-定时 1-周期 0-触发
     */
    private Integer taskScheduler;
    /**
     * 执行周期
     */
    private String taskCron;
    /**
     * 是否批量执行
     */
    private Integer partExec;
    /**
     * 是否忽略
     */
    private Integer ignore;
    /**
     * 任务来源：0脚本服务化，1工具箱，2定时任务，第三方外部系统调用传0
     */
    private Integer startType;
    /**
     * 创建日期
     */
    
    private Timestamp createTime;

    /**
     * 其它模块任务id
     */
    private Long callerTaskId;

    public Long getCallerTaskId() {
        return callerTaskId;
    }

    public void setCallerTaskId(Long callerTaskId) {
        this.callerTaskId = callerTaskId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setScriptTaskId(Long scriptTaskId) {
        this.scriptTaskId = scriptTaskId;
    }

    public Long getScriptTaskId() {
        return scriptTaskId;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }


    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Timestamp getEndTime() {
        return endTime;
    }


    public void setServerNum(Integer serverNum) {
        this.serverNum = serverNum;
    }

    public Integer getServerNum() {
        return serverNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Integer getRunNums() {
        return runNums;
    }

    public void setRunNums(Integer runNums) {
        this.runNums = runNums;
    }

    public Integer getTaskScheduler() {
        return taskScheduler;
    }

    public void setTaskScheduler(Integer taskScheduler) {
        this.taskScheduler = taskScheduler;
    }

    public void setTaskCron(String taskCron) {
        this.taskCron = taskCron;
    }

    public String getTaskCron() {
        return taskCron;
    }

    public Integer getPartExec() {
        return partExec;
    }

    public void setPartExec(Integer partExec) {
        this.partExec = partExec;
    }

    public Integer getIgnore() {
        return ignore;
    }

    public void setIgnore(Integer ignore) {
        this.ignore = ignore;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("scriptTaskId", getScriptTaskId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("status", getStatus())
                .append("startTime", getStartTime())
                .append("endTime", getEndTime())
                .append("runNums", getRunNums())
                .append("serverNum", getServerNum())
                .append("taskScheduler", getTaskScheduler())
                .append("taskCron", getTaskCron())
                .append("partExec", getPartExec())
                .append("ignore", getIgnore())
                .append("startType", getStartType())
                .append("createTime", getCreateTime())
                .toString();
    }
}
