package com.ideal.script.mapper;

import com.ideal.script.dto.ScriptCategoryIconDto;
import com.ideal.script.dto.ScriptInfoQueryDto;
import com.ideal.script.model.entity.Info;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 */
public interface InfoMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
     Info selectInfoById(Long id);

    /**
     * 获取脚本info信息
     * @param scriptInfoQueryDto 查询参数
     * @return 脚本info信息
     */
    Info getScriptInfo(@Param("scriptInfoQueryDto") ScriptInfoQueryDto scriptInfoQueryDto);

    /**
     * 查询
     * @param id    主键id
     * @return  结果
     */
     List<Info> selectInfoByIds(Long[] id);

    /**
     * 查询列表
     * 
     * @param info 
     * @return 集合
     */
     List<Info> selectInfoList(Info info);

    /**
     * 新增
     * 
     * @param info 
     * @return 结果
     */
     int insertInfo(Info info);

    /**
     * 修改
     * 
     * @param info 
     * @return 结果
     */
     int updateInfo(Info info);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
     int deleteInfoById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteInfoByIds(@Param("ids")Long[] ids);
    
    
	/**
	 *  对接巡检查询脚本列表
	 * @param scriptId  脚本id
	 * @param scriptName    脚本名
	 * @param excludeScriptId   不需要的脚本id
	 * @return  结果
	 */
	List<Info> selectInfoListApi(Long scriptId, String scriptName,@Param("excludeScriptId") List<Long> excludeScriptId);

    /**
     * 查询相同脚本名条数
     *
     * @param scriptName 脚本名
     * @return 结果
     */
    int getSameScriptNameCount(String scriptName);

    /**
     * 查询脚本信息
     *
     * @param uniqueUuid uuid
     * @return Info
     */
    Info selectInfoByUniqueUuid(String uniqueUuid);

    /**
     * 验证是否存在相同脚本中文名的脚本
     *
     * @param scriptNameZh 脚本中文名
     * @return Boolean
     */
    Boolean validScriptNameZhCountExist(String scriptNameZh);

    /**
     * 根据脚本英文名查询脚本信息
     * @param scriptName    脚本英文名
     * @return  脚本信息
     */
    Info selectInfoByScriptName(String scriptName);

    /**
     * 根据脚本中文名文名查询脚本信息
     * @param scriptNameZh    脚本中文名
     * @return  脚本信息
     */
    Info selectInfoByScriptNameZh(String scriptNameZh);
    /**
     * 根据uuid进行批量更新数据
     * @param info 脚本基本信息
     * @param uuids uuid集合
     */
    void updateInfosByUuid(@Param("Info") Info info, @Param("uuids") List<String> uuids);

    /**
     * 查询info表uniqueUuid
     * @param ids   info主键
     * @param scriptState   脚本状态
     * @return  info表uniqueUuid
     */
    List<String> getUniqueUuidByIds(List<Long> ids,String scriptState);

    /**
     * 根据info表的uuid更新info表
     * @param info    info
     */
    void updateInfoByUniqueUuid(Info info);

    /**
     * 根据脚本id查询当前默认已发布的脚本
     * @param scriptIds 脚本id
     * @return Long []
     */
    List<Long> getDefaultVersionIdsByScriptId(Long[] scriptIds);

    /**
     * 根据脚本uuid查询脚本信息
     *
     * @param scriptUuid 脚本uuid
     * @return Info
     */
    Info selectInfoByScriptUuid(String scriptUuid);

    /**
     * 获取标签列表
     * @return 标签列表集合
     */
    List<String> getLabelList();

    /**
     * 获取相同中文名数量
     * @param scriptNameZh 脚本中文名
     * @return 脚本数量
     */
    int getSameScriptNameZhCount(String scriptNameZh);

    /**
     * 根据uniqueUuid获取脚本的最大版本号
     * @param uniqueUuid uniqueUuid
     * @return 最大版本的uuid
     */
    String getMaxVersinByUniqueUuid(String uniqueUuid);

    /**
     * 根据脚本uuid获取图标
     *
     * @param srcScriptUuids 脚本srcUuid集合
     * @return 图标和脚本uuid对应关系列表
     */
    List<ScriptCategoryIconDto> getScriptCategoryIconList(List<String> srcScriptUuids);

}
