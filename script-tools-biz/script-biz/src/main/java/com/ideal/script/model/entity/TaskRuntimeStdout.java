package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * agent运行实例标准输出 ieai_script_agent_stdout
 *
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class TaskRuntimeStdout implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @IdGenerator
    @Min(value = 0)
    private Long iid;
    /**
     * 任务实例id
     */
    private Long itaskInstanceId;
    /**
     * agent实例id
     */
    private Long iruntimeId;
    /**
     * agent标准输出
     */
    private String istdout;
    /**
     * agent异常输出
     */
    private String istderror;
    /**
     * 创建时间
     */
    private Timestamp icreateTime;

    public Long getIid() {
        return iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    public Long getItaskInstanceId() {
        return itaskInstanceId;
    }

    public void setItaskInstanceId(Long itaskInstanceId) {
        this.itaskInstanceId = itaskInstanceId;
    }

    public Long getIruntimeId() {
        return iruntimeId;
    }

    public void setIruntimeId(Long iruntimeId) {
        this.iruntimeId = iruntimeId;
    }

    public String getIstdout() {
        return istdout;
    }

    public void setIstdout(String istdout) {
        this.istdout = istdout;
    }

    public String getIstderror() {
        return istderror;
    }

    public void setIstderror(String istderror) {
        this.istderror = istderror;
    }

    public Timestamp getIcreateTime() {
        return icreateTime;
    }

    public void setIcreateTime(Timestamp icreateTime) {
        this.icreateTime = icreateTime;
    }
}
