package com.ideal.script.common.util;

import com.ideal.script.common.constant.enums.Enums;

/**
 * <AUTHOR>
 */
public class StateConverter {
    private StateConverter() {
    }

    /**
     * 将审批服务的审核结果状态转化为脚本服务的状态值
     *
     * @param originalStatus 审批服务审核结果
     * @return {@link Long }
     * <AUTHOR>
     */
    public static Integer convertStatus(Integer originalStatus) {
        switch (originalStatus) {
            // 提交
            case 0:
                return Enums.AuditState.APPROVING.getValue();
            // 通过
            case 1:
                return Enums.AuditState.APPROVED.getValue();
            // 打回
            case 2:
                return Enums.AuditState.REJECTED.getValue();
            default:
                // 其他状态保持不变
                return originalStatus;
        }
    }
}
