package com.ideal.script.mapper;

import com.ideal.script.model.bean.CategoryOrgBean;
import com.ideal.script.model.bean.CategoryRoleBean;
import com.ideal.script.model.bean.CategoryUserBean;
import com.ideal.script.model.bean.OrgBean;
import com.ideal.script.model.bean.UserBean;
import com.ideal.script.model.entity.Category;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper接口
 *
 * <AUTHOR>
 */
public interface CategoryMapper {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    Category selectCategoryById(Long id);

    /**
     * 查询列表
     *
     * @param category 
     * @return 集合
     */
    List<Category> selectCategoryList(Category category);

    /**
     * 新增
     *
     * @param category 
     * @return 结果
     */
    int insertCategory(Category category);

    /**
     * 修改
     *
     * @param category 
     * @return 结果
     */
    int updateCategory(Category category);

    /**
     * 删除
     *
     * @param id 主键
     * @return 结果
     */
    int deleteCategoryById(Long id);

    /**
     * 批量删除
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCategoryByIds(@Param("ids")Long[] ids);

    /**
     * 查询
     *
     * @param childId 子id
     * @return 结果
     */
    Category getParent(Long childId);

    /**
     * 查询
     *
     * @param ids id数组
     * @return 结果
     */
    List<Category> selectCategoryByIds(@Param("ids")Long[] ids);

    /**
     * 校验
     *
     * @param name 名称
     * @return 结果
     */
    List<Category> checkFirstCategoryExist(String name);

    /**
     * 根据编码查询分类数据
     * @param code 分类编码
     * @return 分类对象
     */
    List<Category> getCategoryByCode(String code);

    /**
     * 查询出未绑定任何角色的分类
     * @return 分类结果集
     */
    List<Category> getCategoryWithOutRoleRelation();

    /**
     * 校验
     *
     * @param name 名称
     * @param iid  主键id
     * @return 结果
     */
    List<Category> checkNextCategoryExist(String name, Long iid);

    /**
     * 查询
     *
     * @param categoryId 分类id
     * @return 结果
     */
     List<Long> selectChildCategoryIdList(Long categoryId);
    /**
     *获取当前分类子类id
     * @param categoryId     分类id
     * @return     结果
     */
    List<Long> getCategoryIds(List<Long> categoryId);


    /**
     * 功能描述： 通过分类等级、分类名称、所属父分类Id查找分类信息
     *
     * @param categoryQuery 分类查询条件
     * @return {@link Category }
     * <AUTHOR>
     */
    List<Category> findByLevelAndNameAndParentId(Category categoryQuery);

    /**
     * 查询当前分类下的子分类Id集合
     *
     * @param categoryId 当前分类Id
     * @return {@link List }<{@link Long }>
     */
    List<Long> getSubCategoryIds(Long categoryId);

    /**
     * 查询分类是否被引用
     *
     * @param categoryIds 分类Id
     * @return {@link Integer }
     */
    Integer getCategoryReferencedCount(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 根据scriptInfoVersionId查询分类信息
     * @param scriptInfoVersionId 脚本版本id
     * @return 返回分类对象
     */
    Category getCategoryByScriptInfoVersionId(@Param("scriptInfoVersionId") Long scriptInfoVersionId);

    /**
     * 分类授权部门
     * @param categoryOrgBean 实体
     */
    int insertCategoryDepartment(CategoryOrgBean categoryOrgBean);


    /**
     * 查询该分类的部门绑定信息
     * @param categoryId    分类id
     * @return  详细信息
     */
    List<OrgBean> getCategoryOrgRelations(long categoryId);

    /**
     * 通过分类部门关系表主键删除绑定关系
     * @param ids   关系表主键
     */
    void deleteCategoryDepartmentByIds(List<Long> ids);

    /**
     * 根据部门编码获取分类id
     * @param orgCode   部门编码
     * @return  分类id
     */
    List<Long> getCategoryByOrgCode(String orgCode);

    /**
     * 根据角色id获取分类id
     * @param roleIds 角色id集合
     * @return 分类id集合
     */
    List<Long> getCategoryIdsByRoleIds(List<Long> roleIds);

    /**
     * 根据角色id获取所有绑定到该角色的分类与所有未绑定任何分类的id
     * @param roleIds 角色id
     * @return 分类id集合
     */
    List<Long> getSaveRoleCategoryIdsByRoleIds(List<Long> roleIds);

    /**
     * 根据角色id获取分类对象
     * @param roleIds 角色id集合
     * @return 分类id集合
     */
    List<CategoryRoleBean> getCategoryIdsByRole(List<Long> roleIds);
    /**
     * 根据分类id查询用户信息
     * @param categoryId    分类id
     * @return  用户列表
     */
    List<UserBean> getCategoryUsertRelations(Long categoryId);

    /**
     * 删除分类用户绑定关系根据id
     * @param ids   主键列表
     */
    void deleteCategoryUserByIds(List<Long> ids);

    /**
     * 根据分类id删除审核人
     * @param categoryIds 分类id集合
     */
    void deleteCategoryUserByCategoryIds(List<Long> categoryIds);

    /**
     * 分类授权用户
     * @param categoryUserBean 实体
     */
    int insertCategoryUser(CategoryUserBean categoryUserBean);


    /**
     * 查询全部分类
     * @return  分类列表
     */
    List<Category> selectAllCategories();

    /**
     * 查询所有部门分类绑定关系
     * @return  部门分类绑定关系列表
     */
    List<OrgBean> selectAllOrgRelations();

    /**
     * 通过分类id列表查询分类-用户关系表中对应的用户
     * @param categoryIdList    分类id列表
     * @return  用户集合
     */
    List<UserBean> getUserByCategoryIds(List<Long> categoryIdList);

    /**
     * 校验存在的id有哪些
     * @param ids 分类id列表
     * @return 存在的分类id列表
     */
    List<Long> checkIdsExist(@Param("ids")Long[] ids);

    /**
     * 根据分类id查询分类角色绑定列表
     * @param categoryId 分类id
     * @return 分类角色绑定列表
     */
    List<CategoryRoleBean> getCategoryRoleRelations(Long categoryId);

    /**
     * 新增分类角色绑定
     * @param categoryRoleBean
     * @return
     */
    void insertCategoryRole(CategoryRoleBean categoryRoleBean);

    /**
     * 删除分类角色绑定
     * @param ids id集合
     * @return
     */
    void deleteCategoryRoleByIds(List<Long> ids);

    /**
     * 根据分类id删除角色-分类绑定关系
     * @param categoryIds 分类id集合
     */
    void deleteCategoryRoleByCategoryIds(List<Long> categoryIds);

    /**
     * 根据一级分类名称列表查询一级分类信息列表
     * @param categoryNameList 一级分类名称列表
     * @return 一级分类信息列表
     */
    List<Category> getFirstCategoryByCategoryNameList(List<String> categoryNameList);
}
