package com.ideal.script.common.util;

import com.ideal.script.common.constant.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.security.*;


/**
 * 添加Agent服务器信息密码工具类
 *
 * <AUTHOR>
 */
public class DesUtils
{
    private static final Logger log = LoggerFactory.getLogger(DesUtils.class);
    /** 字符串默认键值 */
    private static final String STR_DEFAULT_KEY = "national";

    private static final String IBM="IBM";

    private static final int TWO = 2;

    /** 加密工具 */
    private Cipher        encryptCipher = null;

    /** 解密工具 */
    private Cipher        decryptCipher = null;

    /**
     * 将byte数组转换为表示16进制值的字符串， 如：byte[]{8,18}转换为：0813， 和public static byte[] hexStr2ByteArr(String
     * strIn) 互为可逆的转换过程
     * 
     * @param arrB 需要转换的byte数组
     * @return 转换后的字符串
     */
    public static String byteArr2HexStr ( byte[] arrB ) 
    {
        int iLen = arrB.length;
        // 每个byte用两个字符才能表示，所以字符串的长度是数组长度的两倍
        StringBuilder sb = new StringBuilder(iLen * 2);
        try
        {
            for (int b : arrB) {
                int intTmp = b;
                // 把负数转换为正数
                while (intTmp < 0) {
                    intTmp = intTmp + 256;
                }
                // 小于0F的数需要在前面补0
                if (intTmp < 16) {
                    sb.append("0");
                }
                sb.append(Integer.toString(intTmp, 16));
            }
        } catch (Exception e)
        {
            log.error("byteArr2HexStr has Exception: {} ",e.getMessage());
        }
        return sb.toString();
    }

    /**
     * 将表示16进制值的字符串转换为byte数组， 和public static String byteArr2HexStr(byte[] arrB) 互为可逆的转换过程
     * 
     * @param strIn 需要转换的字符串
     * @return 转换后的byte数组
     * <AUTHOR> href="mailto:<EMAIL>">LiGuoQing</a>
     */
    public static byte[] hexStr2ByteArr ( String strIn ) 
    {
        byte[] arrB = strIn.getBytes();
        int iLen = arrB.length;
        byte[] arrOut = null;
        try
        {
         // 两个字符表示一个字节，所以字节数组长度是字符串长度除以2
            arrOut = new byte[iLen / 2];
            for (int i = 0; i < iLen; i = i + TWO)
            {
                String strTmp = new String(arrB, i, 2);
                arrOut[i / 2] = (byte) Integer.parseInt(strTmp, 16);
            }
        } catch (Exception e)
        {
            log.error("hexStr2ByteArr has Exception:{} ",e.getMessage());
        }
        return arrOut;
    }

    /**
     * 默认构造方法，使用默认密钥
     *
     */
    @SuppressWarnings("unused")
    public DesUtils()
    {
        this(STR_DEFAULT_KEY);
    }

    /**
     * 指定密钥构造方法
     * 
     * @param strKey 指定的密钥
     */
    public DesUtils(String strKey)
    {
        String jdkvs=System.getProperty("java.vm.vendor");
        try{
            if(null!=jdkvs && jdkvs.startsWith(IBM)){
                Security.addProvider((Provider)Class.forName("com.ibm.crypto.provider.IBMJCE").getDeclaredConstructor().newInstance());
            }else{
                Security.addProvider((Provider)Class.forName("com.sun.crypto.provider.SunJCE").getDeclaredConstructor().newInstance());
            }
        }catch(Exception e){
            log.error("loading provicer.JCE ERROR",e);
        }
        Key key = getKey(strKey.getBytes());

        try
        {
            encryptCipher = Cipher.getInstance(Constants.ALGORITHM);
            encryptCipher.init(Cipher.ENCRYPT_MODE, key);

            decryptCipher = Cipher.getInstance(Constants.ALGORITHM );
            decryptCipher.init(Cipher.DECRYPT_MODE, key);
        } catch (NoSuchAlgorithmException e)
        {
            log.error("DesUtils has NoSuchAlgorithmException:{} ",e.getMessage());
        } catch (NoSuchPaddingException e)
        {
            log.error("DesUtils has NoSuchPaddingException:{} ",e.getMessage());
        } catch (InvalidKeyException e)
        {
            log.error("DesUtils has InvalidKeyException:{} ",e.getMessage());
        }
        
    }

    /**
     * 加密字节数组
     * 
     * @param arrB 需加密的字节数组
     * @return 加密后的字节数组
     */
    public byte[] encrypt ( byte[] arrB ) throws IllegalBlockSizeException, BadPaddingException 
    {
        
        return encryptCipher.doFinal(arrB);
    }

    /**
     * 加密字符串
     * 
     * @param strIn 需加密的字符串
     * @return 加密后的字符串
     */
    public String encrypt ( String strIn ) throws IllegalBlockSizeException, BadPaddingException
    {
        return byteArr2HexStr(encrypt(strIn.getBytes()));
    }

    /**
     * 解密字节数组
     * 
     * @param arrB 需解密的字节数组
     * @return 解密后的字节数组
     */
    public byte[] decrypt ( byte[] arrB ) throws IllegalBlockSizeException, BadPaddingException 
    {
        return decryptCipher.doFinal(arrB);
    }

    /**
     * 解密字符串
     * 
     * @param strIn 需解密的字符串
     * @return 解密后的字符串
     */
    public String decrypt ( String strIn ) throws IllegalBlockSizeException, BadPaddingException 
    {
        return new String(decrypt(hexStr2ByteArr(strIn)));
    }

    /**
     * 解密字符串
     *
     * @param strIn 需解密的字符串
     * @return 解密后的字符串
     */
    @SuppressWarnings("unused")
    public String decrypt ( String strIn, String charset ) throws IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        return new String(decrypt(hexStr2ByteArr(strIn)), charset);
    }
    /**
     * 从指定字符串生成密钥，密钥所需的字节数组长度为8位 不足8位时后面补0，超出8位只取前8位
     * 
     * @param byteArr 构成该字符串的字节数组
     * @return 生成的密钥
     */
    private Key getKey ( byte[] byteArr)
    {
        Key key = null;
        try{
            // 创建一个空的8位字节数组（默认值为0）
            byte[] arrB = new byte[8];

            // 将原始字节数组转换为8位
            for (int i = 0; i < byteArr.length && i < arrB.length; i++)
            {
                arrB[i] = byteArr[i];
            }

            // 生成密钥
            key = new javax.crypto.spec.SecretKeySpec(arrB, "DES");
        }catch(Exception e){
            log.error("DesUtils has InvalidKeyException:{} ",e.getMessage());
        }

        return key;
    }
}
