package com.ideal.script.common.util;

import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.exception.ScriptException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.unit.DataSize;

@Component("fileSizeValidUtil")
public class FileSizeValidUtil {
    private final Logger logger = LoggerFactory.getLogger(FileSizeValidUtil.class);
    private final ScriptBusinessConfig scriptBusinessConfig;
    public FileSizeValidUtil(ScriptBusinessConfig scriptBusinessConfig) {
        this.scriptBusinessConfig = scriptBusinessConfig;
    }

    /**
     * 验证文件大小是否满足要求
     * @param fileSize  文件大小
     */
    public void validateFileSize(Long fileSize) throws ScriptException {
        //验证单个文件大小和整体文件大小,从配置文件中获取文件大小
        DataSize attachmentLimitSize = scriptBusinessConfig.getEachScriptAttachmentLimitSize();
        long limitSizeMegabytes = attachmentLimitSize.toBytes();
        if(fileSize > limitSizeMegabytes){
            logger.error("file.too.large");
            throw new ScriptException("file.too.large");
        }

    }
}
