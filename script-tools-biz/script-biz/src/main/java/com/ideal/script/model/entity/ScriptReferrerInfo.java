package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
public class ScriptReferrerInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @IdGenerator
    @Min(value = 0)
    private Long iid;

    /**
     * 场景名称
     */
    private String sceneName;
    /**
     * 流程名称
     */
    private String flowName;
    /**
     * 流程版本
     */
    private String flowVersion;
    /**
     * 引用对象名称
     */
    private String referrerName;
    /**
     * 引用对象id
     */
    private Long referrerBizId;
    /**
     * 脚本版本uuid,消费mq可能是多条，使用数据形式
     */
    private String [] scriptSrcUuid;

    /**
     * 脚本版本uuid，新增数据的时候使用
     */
    private String scriptSrcUuidSingle;
    /**
     * 创建时间
     */
    private Timestamp createdTime;
    /**
     * 脚本英文名
     */
    private String scriptName;
    /**
     * 脚本中文名
     */
    private String scriptNameZh;
    /**
     * 脚本版本号
     */
    private String scriptVersion;
    /**
     * 操作类型：save为新增，delete为删除
     */
    private String bizType;
    /**
     * 脚本uniqueUuid
     */
    private String uniqueUuid;

    public String getUniqueUuid() {
        return uniqueUuid;
    }

    public void setUniqueUuid(String uniqueUuid) {
        this.uniqueUuid = uniqueUuid;
    }

    public Long getIid() {
        return iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public String getFlowVersion() {
        return flowVersion;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public String getReferrerName() {
        return referrerName;
    }

    public void setReferrerName(String referrerName) {
        this.referrerName = referrerName;
    }

    public Long getReferrerBizId() {
        return referrerBizId;
    }

    public void setReferrerBizId(Long referrerBizId) {
        this.referrerBizId = referrerBizId;
    }

    public String[] getScriptSrcUuid() {
        return scriptSrcUuid;
    }

    public void setScriptSrcUuid(String[] scriptSrcUuid) {
        this.scriptSrcUuid = scriptSrcUuid;
    }

    public String getScriptSrcUuidSingle() {
        return scriptSrcUuidSingle;
    }

    public void setScriptSrcUuidSingle(String scriptSrcUuidSingle) {
        this.scriptSrcUuidSingle = scriptSrcUuidSingle;
    }

    public Timestamp getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Timestamp createdTime) {
        this.createdTime = createdTime;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptVersion() {
        return scriptVersion;
    }

    public void setScriptVersion(String scriptVersion) {
        this.scriptVersion = scriptVersion;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }
}
