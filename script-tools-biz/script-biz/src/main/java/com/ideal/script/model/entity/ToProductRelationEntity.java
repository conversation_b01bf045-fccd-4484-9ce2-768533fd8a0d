package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 投产记录关系对象 ieai_script_to_product_relation
 *
 * <AUTHOR>
 */
public class ToProductRelationEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @IdGenerator
    @Min(value = 0)
    private Long id;
    /** ieai_script_toproduct表的id */
    private Long scriptToproductId;
    /** 文件内容json字符串 */
    private String srcScriptUuid;
    /** 创建时间 */
    
    private Timestamp createTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setScriptToproductId(Long scriptToproductId){
        this.scriptToproductId = scriptToproductId;
    }

    public Long getScriptToproductId(){
        return scriptToproductId;
    }

    public void setSrcScriptUuid(String srcScriptUuid){
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid(){
        return srcScriptUuid;
    }

    public void setCreateTime(Timestamp createTime){
        this.createTime = createTime;
    }

    public Timestamp getCreateTime(){
        return createTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",scriptToproductId="+getScriptToproductId()+
                    ",srcScriptUuid="+getSrcScriptUuid()+
                    ",createTime="+getCreateTime()+
                "]";
    }
}

