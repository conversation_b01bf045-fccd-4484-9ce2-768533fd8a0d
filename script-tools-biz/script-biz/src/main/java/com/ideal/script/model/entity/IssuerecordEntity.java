package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 脚本下发的对象 ieai_script_issuerecord
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public class IssuerecordEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @IdGenerator
    @Min(value = 0)
    private Long id;
    /** ieai_script_info_version表的isrc_script_uuid */
    private String srcScriptUuid;
    /** AGENTIP */
    private String agentIp;
    /** AGENT端口 */
    private Integer agentPort;
    /** 状态1-成功2-运行中3-异常4-MD5不一致 */
    private Integer status;
    /** 消息 */
    private String message;
    /** 下发路径 */
    private String sendPath;
    /** 文件权限(默认755) */
    private String chmod;
    /** 用户权限 */
    private String userPermission;
    /** 用户组权限 */
    private String groupPermission;
    /** 下发人ID */
    private Long sendUserId;
    /** 下发人名称 */
    private String sendUserName;
    /** 下发时间 */
    private Timestamp sendTime;

    /**
     * 批次号（时间戳）
     */
    private String batchNumber;
    private String bizId;

    /**
     * 脚本中文名称
     */
    private String scriptNameZh;

    /**
     * 脚本名称
     */
    private String scriptName;

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setSrcScriptUuid(String srcScriptUuid){
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getSrcScriptUuid(){
        return srcScriptUuid;
    }

    public void setAgentIp(String agentIp){
        this.agentIp = agentIp;
    }

    public String getAgentIp(){
        return agentIp;
    }

    public void setAgentPort(Integer agentPort){
        this.agentPort = agentPort;
    }

    public Integer getAgentPort(){
        return agentPort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setMessage(String message){
        this.message = message;
    }

    public String getMessage(){
        return message;
    }

    public void setSendPath(String sendPath){
        this.sendPath = sendPath;
    }

    public String getSendPath(){
        return sendPath;
    }

    public void setChmod(String chmod){
        this.chmod = chmod;
    }

    public String getChmod(){
        return chmod;
    }

    public void setUserPermission(String userPermission){
        this.userPermission = userPermission;
    }

    public String getUserPermission(){
        return userPermission;
    }

    public void setGroupPermission(String groupPermission){
        this.groupPermission = groupPermission;
    }

    public String getGroupPermission(){
        return groupPermission;
    }

    public void setSendUserId(Long sendUserId){
        this.sendUserId = sendUserId;
    }

    public Long getSendUserId(){
        return sendUserId;
    }

    public void setSendUserName(String sendUserName){
        this.sendUserName = sendUserName;
    }

    public String getSendUserName(){
        return sendUserName;
    }

    public void setSendTime(Timestamp sendTime){
        this.sendTime = sendTime;
    }

    public Timestamp getSendTime(){
        return sendTime;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",srcScriptUuid="+getSrcScriptUuid()+
                    ",agentIp="+getAgentIp()+
                    ",agentPort="+getAgentPort()+
                    ",status="+getStatus()+
                    ",message="+getMessage()+
                    ",sendPath="+getSendPath()+
                    ",chmod="+getChmod()+
                    ",userPermission="+getUserPermission()+
                    ",groupPermission="+getGroupPermission()+
                    ",sendUserId="+getSendUserId()+
                    ",sendUserName="+getSendUserName()+
                    ",sendTime="+getSendTime()+
                "]";
    }
}

