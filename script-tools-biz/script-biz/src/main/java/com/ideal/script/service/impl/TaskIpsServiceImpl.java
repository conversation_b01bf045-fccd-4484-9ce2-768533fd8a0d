package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskIpsDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.model.entity.TaskIps;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.ITaskIpsService;
import org.apache.ibatis.session.SqlSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 任务与agent关系Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class TaskIpsServiceImpl implements ITaskIpsService {
    private static final Logger logger = LoggerFactory.getLogger(TaskIpsServiceImpl.class);

    private final TaskIpsMapper taskIpsMapper;
    private final IAgentInfoService agentInfoService;
    private final BatchDataUtil batchDataUtil;
    public TaskIpsServiceImpl(TaskIpsMapper taskIpsMapper, IAgentInfoService agentInfoService, BatchDataUtil batchDataUtil) {
        this.taskIpsMapper = taskIpsMapper;
        this.agentInfoService = agentInfoService;
        this.batchDataUtil = batchDataUtil;
    }

    /**
     * 查询任务与agent关系
     *
     * @param id 任务与agent关系主键
     * @return 任务与agent关系
     */
    @Override
    public TaskIpsDto selectTaskIpsById(Long id) {
        return BeanUtils.copy(taskIpsMapper.selectTaskIpsById(id), TaskIpsDto.class);
    }

    /**
     * 查询任务与agent关系列表
     *
     * @param taskIpsDto 任务与agent关系
     * @return 任务与agent关系
     */
    @Override
    public PageInfo<TaskIpsDto> selectTaskIpsList(TaskIpsDto taskIpsDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<TaskIps> taskIpsList = new ArrayList<>();
        if (null != taskIpsDto) {
            TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
            taskIpsList = taskIpsMapper.selectTaskIpsList(taskIps);
        }
        return PageDataUtil.toDtoPage(taskIpsList, TaskIpsDto.class);

    }

    /**
     * 新增任务与agent关系
     *
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
    @Override
    public int insertTaskIps(TaskIpsDto taskIpsDto) {
        TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
        return taskIpsMapper.insertTaskIps(taskIps);
    }

    /**
     * 修改任务与agent关系
     *
     * @param taskIpsDto 任务与agent关系
     * @return 结果
     */
    @Override
    public int updateTaskIps(TaskIpsDto taskIpsDto) {
        TaskIps taskIps = BeanUtils.copy(taskIpsDto, TaskIps.class);
        return taskIpsMapper.updateTaskIps(taskIps);
    }

    /**
     * 批量删除任务与agent关系
     *
     * @param ids 需要删除的任务与agent关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskIpsByIds(Long[] ids) {
        return taskIpsMapper.deleteTaskIpsByIds(ids);
    }

    /**
     * 删除任务与agent关系信息
     *
     * @param id 任务与agent关系主键
     * @return 结果
     */
    @Override
    public int deleteTaskIpsById(Long id) {
        return taskIpsMapper.deleteTaskIpsById(id);
    }

    /**
     * 删除任务与agent关系信息
     *
     * @param taskId 任务id
     * @return 结果
     */
    @Override
    public int deleteTaskIpsByTaskId(Long taskId) {
        return taskIpsMapper.deleteTaskIpsByTaskId(taskId);
    }


    /**
     * 存储agent信息
     *
     * @param scriptExecAuditDto 任务申请提交审核Dto
     * @param taskInfo           任务申请提交任务时的脚本任务对象
     * @param sqlSession         ​SqlSession​ 对象
     * <AUTHOR>
     */
    @Override
    public void saveTaskIps(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, SqlSession sqlSession) throws ScriptException {
        List<TaskIps> taskIpsList = buildTaskIpsList(scriptExecAuditDto, taskInfo);

        if (!taskIpsList.isEmpty()) {
            TaskIpsMapper taskIpsMapperSession = sqlSession.getMapper(TaskIpsMapper.class);
            try {
                batchDataUtil.batchData(TaskIpsMapper.class, taskIpsList, TaskIps.class, "insertTaskIps", sqlSession, taskIpsMapperSession);
            } catch (ScriptException ex) {
                logger.error("saveTaskIps error:", ex);
                throw new ScriptException("save.taskIps.error");
            }
        }

    }


    private List<TaskIps> buildTaskIpsList(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo) throws ScriptException {
        List<TaskIps> taskIpsList = new ArrayList<>();
        List<AgentInfoDto> agentUsers = scriptExecAuditDto.getChosedAgentUsers();
        if (null != agentUsers) {
            for (AgentInfoDto agentInfoDto : agentUsers) {
                logger.debug("任务申请ip数据处理 agentInfoDto：{}", agentInfoDto);

                // 检查ieai_script_agent_info表中是否已存在相同的记录(IP+端口)
                boolean exists = agentInfoService.checkAgentInfoExists(agentInfoDto.getAgentIp(), agentInfoDto.getAgentPort().longValue());
                if (!exists) {
                    // 存储ieai_script_agent_info表信息
                    // 脚本服务化插入ip数据以IDGenerate注解为准，重置ID属性
                    agentInfoDto.setId(null);
                    agentInfoService.insertAgentInfo(agentInfoDto);
                } else {
                    AgentInfoDto dto = new AgentInfoDto();
                    dto.setAgentIp(agentInfoDto.getAgentIp());
                    dto.setAgentPort(agentInfoDto.getAgentPort());
                    AgentInfo agentInfo = agentInfoService.selectAgentInfoByIpAndPort(dto);
                    agentInfoDto.setId(agentInfo.getId());
                }

                // 设置执行用
                TaskIps taskIps = getTaskIps(scriptExecAuditDto, taskInfo, agentInfoDto);
                taskIpsList.add(taskIps);
            }
        }
        return taskIpsList;
    }

    /**
     * 组织任务与agent关系对象
     * @param scriptExecAuditDto    脚本任务提交审核Dto
     * @param taskInfo  运行时任务基础信息
     * @param agentInfoDto  Agent基本信息
     * @return  任务与agent关系对象信息
     */
    private static TaskIps getTaskIps(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, AgentInfoDto agentInfoDto) {
        TaskIps taskIps = new TaskIps();
        taskIps.setId(null);
        taskIps.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
        taskIps.setScriptAgentinfoId(agentInfoDto.getId());
        taskIps.setExecUserName(agentInfoDto.getExecUserName() == null || ("").equals(agentInfoDto.getExecUserName()) ? scriptExecAuditDto.getExecuser() : agentInfoDto.getExecUserName());
        taskIps.setAlreadyimpFlag(Enums.ExecutionStatus.NOT_EXECUTED.getValue());
        taskIps.setStartType(Enums.BusinessType.SCRIPT_SERVICE.getValue());
        return taskIps;
    }

}
