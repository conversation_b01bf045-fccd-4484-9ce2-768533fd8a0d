package com.ideal.script.service.impl;

import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.dto.AuditRelationDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.service.AuditSource;
import com.ideal.script.service.IAuditRelationService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.ITaskService;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 任务申请
 */
@Service("taskApplySource")
public class TaskApplySource implements AuditSource {
    private final Logger logger = LoggerFactory.getLogger(TaskApplySource.class);
    private final IAuditRelationService auditRelationService;
    private final IInfoVersionService infoVersionService;
    private final ITaskService taskService;

    public TaskApplySource(IAuditRelationService auditRelationService, IInfoVersionService infoVersionService, ITaskService taskService) {
        this.auditRelationService = auditRelationService;
        this.infoVersionService = infoVersionService;
        this.taskService = taskService;
    }

    @Override
    public void preHandle(ScriptExecAuditDto scriptExecAuditDto) {

    }

    @Override
    public Long getRelationId(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskInfo, String srcScriptUuid) throws ScriptException {
        Long auditRelationId;
        try {
            //存储双人复核与脚本服务化关系
            AuditRelationDto auditRelationDto = new AuditRelationDto();
            auditRelationDto.setScriptTaskId(taskInfo != null ? taskInfo.getId() : null);
            auditRelationDto.setSrcScriptUuid(srcScriptUuid);
            auditRelationDto.setAuditType(Enums.AuditType.SCRIPT_TASK.getValue());
            auditRelationDto.setState(Enums.AuditState.APPROVING.getValue());

            // 审核人
            auditRelationDto.setAuditUser(scriptExecAuditDto.getAuditUser());
            auditRelationDto.setAuditUserId(scriptExecAuditDto.getAuditUserId());

            CurrentUser currentUser = CurrentUserUtil.getCurrentUser();
            // 申请人ser
            auditRelationDto.setApplyUser(currentUser.getLoginName());
            auditRelationDto.setApplyUserId(currentUser.getId());

            //工单号
            auditRelationDto.setWorkOrderNumber(scriptExecAuditDto.getWorkOrderNum());

            auditRelationService.insertAuditRelation(auditRelationDto);
            auditRelationId = auditRelationDto.getId();
            logger.info("auditRelationId :{}", auditRelationDto.getId());
        } catch (Exception e) {
            logger.error("saveAuditRelation error :", e);
            throw new ScriptException("storage.audit.relation.failed");
        }
        return auditRelationId;
    }

    @Override
    public void bindTaskId(ScriptExecAuditDto scriptExecAuditDto) {

    }

    /**
     * 是否白名单
     *
     * @param scriptExecAuditDto 脚本任务提交审核Dto
     * @return boolean
     */
    @Override
    public boolean isInWhiteList(ScriptExecAuditDto scriptExecAuditDto) {
        Long scriptInfoVersionId = scriptExecAuditDto.getScriptInfoVersionId();
        ScriptVersionDto infoVersionDto = infoVersionService.selectInfoVersionById(scriptInfoVersionId);
        return infoVersionService.isInWhiteList(infoVersionDto.getId());
    }

    @Override
    public void saveOrUpdateTask(ScriptExecAuditDto scriptExecAuditDto, TaskDto taskDto) throws ScriptException {
        taskService.insertTask(taskDto);
    }

    @Override
    public void preHandleAttachment(Long taskId) {

    }
}
