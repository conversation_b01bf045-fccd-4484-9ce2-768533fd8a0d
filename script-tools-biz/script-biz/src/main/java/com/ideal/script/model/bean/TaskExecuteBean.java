package com.ideal.script.model.bean;

import com.ideal.sc.annotation.GetTimestampDiffField;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 任务Bean，用于封装执行任务菜单的信息。
 * 适用于展示待执行、运行中和执行历史三个选项卡中的任务列表信息。
 *
 * <AUTHOR>
 */
public class TaskExecuteBean implements Serializable, CategoryPermissionAware {

    private static final long serialVersionUID = 1L;

    //脚本任务表属性如下
    /**
     * 主键
     */
    private Long scriptTaskId;


    /**
     * ieai_script_info_version表的isrc_script_uuid
     */
    private String srcScriptUuid;


    /**
     * 任务名称
     */
    private String taskName;


    /**
     * 并发数量
     */
    private Long eachNum;


    /**
     * 执行用户
     */
    private String execUser;


    /**
     * 任务调度方式：2-定时 1-周期 0-触发
     */
    private Integer taskScheduler;


    /**
     * 任务执行时间
     */
    private Timestamp taskTime;


    /**
     * 执行周期
     */
    private String taskCron;


    /**
     * 发布详细描述
     */
    private String publishDesc;


    /**
     * 任务执行人-启动人
     */
    private String startUser;


    /**
     * 任务发起时间
     */
    private Timestamp startTime;


    /**
     * 脚本执行时候的超时时间
     */
    private Long timeout;


    /**
     * 任务展示时间
     */
    private String taskTimeForDispaly;


    /**
     * 任务类型
     */
    private Integer type;


    /**
     * 驱动模式 (1:按勾选执行 2：分批执行 3：忽略异常分批执行 4：队列执行）
     */
    private Integer driveMode;


    /**
     * 任务来源：0脚本服务化，1工具箱，2定时任务，第三方外部系统调用传0
     */
    private Integer startType;

    // 脚本任务是否待执行，审核通过时，状态为待执行。
    /**
     * 脚本任务是否待执行 0 - 不具备执行能力 1 - 待执行（审核通过时）
     */
    private Integer readyToExecute;

    /**
     * 创建人id
     */
    private Long creatorId;


    /**
     * 创建人名称
     */
    private String creatorName;


    /**
     * 修改人id
     */
    private Long updatorId;


    /**
     * 修改人名称
     */
    private String updatorName;


    /**
     * 创建时间
     */
    private Timestamp createTime;


    /**
     * 修改时间
     */
    private Timestamp updateTime;


    //双人复核与脚本服务化关系表属性如下


    /**
     * 主键
     */
    private Long iscriptAuditRelationId;

    /**
     * 双人复核主键
     */
    private Long apprWorkitemId;


    /**
     * 审核类型
     */
    private Integer auditType;


    /**
     * 审核状态
     */
    private Integer state;


    /**
     * 申请时间
     */
    private Timestamp applyTime;


    /**
     * 审核时间
     */
    private Timestamp auditTime;


    /**
     * 审核人
     */
    private String auditUser;


    /**
     * 申请人
     */
    private String applyUser;


    /**
     * 审核人Id
     */
    private Long auditUserId;

    /**
     * 申请人Id
     */
    private Long applyUserId;

    /**
     * 分类名称
     */
    private String scriptCategoryName;

    /**
     * 脚本任务运行实例主键（ieai_script_task_instance表主键）
     */
    private Long scriptTaskInstanceId;

    private Long categoryId;


    /**
     * 单号
     */
    private String workOrderNumber;





    /**
     * 菜单类别（区分菜单任务监控还是定时任务监控）
     */
    private String menuType;




    /**
     * 获取全部任务 true 是 false 否
     */
    private Boolean allTaskFlag;




    /**
     * 开始时间范围
     */
    private List<Timestamp> startTimeRange;

    /**
     * 结束时间范围
     */
    private List<Timestamp> endTimeRange;

    /**
     * 是否只查询自己的数据标识，true只查询自己的数据，默认false
     */
    private boolean selectSelfDataFlag = false;

    public boolean isSelectSelfDataFlag() {
        return selectSelfDataFlag;
    }

    public void setSelectSelfDataFlag(boolean selectSelfDataFlag) {
        this.selectSelfDataFlag = selectSelfDataFlag;
    }

    public List<Timestamp> getStartTimeRange() {
        return startTimeRange;
    }

    public void setStartTimeRange(List<Timestamp> startTimeRange) {
        this.startTimeRange = startTimeRange;
    }

    public List<Timestamp> getEndTimeRange() {
        return endTimeRange;
    }

    public void setEndTimeRange(List<Timestamp> endTimeRange) {
        this.endTimeRange = endTimeRange;
    }

    public Boolean getAllTaskFlag() {
        return allTaskFlag;
    }

    public void setAllTaskFlag(Boolean allTaskFlag) {
        this.allTaskFlag = allTaskFlag;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    /**
     * agent拼接信息 ip:port,ip1:port1(针对定时任务维护导出使用)
     */
    private String agent;

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getWorkOrderNumber() {
        return workOrderNumber;
    }

    public void setWorkOrderNumber(String workOrderNumber) {
        this.workOrderNumber = workOrderNumber;
    }

    @SuppressWarnings("unused")
    public Long getScriptTaskInstanceId() {
        return scriptTaskInstanceId;
    }

    @SuppressWarnings("unused")
    public void setScriptTaskInstanceId(Long scriptTaskInstanceId) {
        this.scriptTaskInstanceId = scriptTaskInstanceId;
    }

    /**
     * 任务运行状态
     */
    private Integer scriptTaskInstanceState;

    /**
     * 表示当前实例正在运行的agent数量
     */
    private Integer runAgentCount;

    /**
     * 风险级别 0 - 白名单，1 - 高风险级别， 2 - 中风险级别， 3 - 低风险级别
     */
    private Integer level;

    /**
     * 脚本版本
     */
    private String version;

    /**
     * 当前分类Id以及子分类Id集合
     */
    private List<Long> categoryIdList;

    /**
     * 耗时
     */
    @GetTimestampDiffField(value = "startTime", otherOne = "endTime")
    private Long elapsedTime;
    /**
     * 任务结束时间
     */
    private Timestamp endTime;
    /**
     * 数据库当前时间
     */
    private Timestamp currentTime;

    /**
     * 脚本任务来源 1：任务申请  2：脚本测试
     */
    private Integer scriptTaskSource;

    /**
     * 脚本中文名
     */
    private String scriptNameZh;

    /**
     * 脚本英文名
     */
    private String scriptName;

    /**
     * 是否走角色权限查询
     */
    private boolean roleFlag = false;

    /**
     * 是否为值班任务申请，true是，false不是，默认false
     */
    private boolean dutyApply = false;

    /**
     * 用户id集合
     */
    private List<Long> userIdList;

    /**
     * 角色id结合
     */
    private List<Long> roleIdList;

    public List<Long> getRoleIdList() {
        return roleIdList;
    }

    @Override
    public void setRoleIdList(List<Long> roleIdList) {
        this.roleIdList = roleIdList;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    @Override
    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public boolean isDutyApply() {
        return dutyApply;
    }

    public void setDutyApply(boolean dutyApply) {
        this.dutyApply = dutyApply;
    }

    public boolean isRoleFlag() {
        return roleFlag;
    }

    public void setRoleFlag(boolean roleFlag) {
        this.roleFlag = roleFlag;
    }

    public String getScriptNameZh() {
        return scriptNameZh;
    }

    public void setScriptNameZh(String scriptNameZh) {
        this.scriptNameZh = scriptNameZh;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public Integer getScriptTaskSource() {
        return scriptTaskSource;
    }

    public void setScriptTaskSource(Integer scriptTaskSource) {
        this.scriptTaskSource = scriptTaskSource;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public List<Long> getCategoryIdList() {
        return categoryIdList;
    }
    @Override
    public void setCategoryIdList(List<Long> categoryIdList) {
        this.categoryIdList = categoryIdList;
    }

    @SuppressWarnings("unused")
    public Integer getRunAgentCount() {
        return runAgentCount;
    }

    @SuppressWarnings("unused")
    public void setRunAgentCount(Integer runAgentCount) {
        this.runAgentCount = runAgentCount;
    }

    @SuppressWarnings("unused")
    public Integer getScriptTaskInstanceState() {
        return scriptTaskInstanceState;
    }

    @SuppressWarnings("unused")
    public void setScriptTaskInstanceState(Integer scriptTaskInstanceState) {
        this.scriptTaskInstanceState = scriptTaskInstanceState;
    }

    @SuppressWarnings("unused")
    public String getScriptCategoryName() {
        return scriptCategoryName;
    }

    @SuppressWarnings("unused")
    public void setScriptCategoryName(String scriptCategoryName) {
        this.scriptCategoryName = scriptCategoryName;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public String getTaskName() {
        return taskName;
    }

    @SuppressWarnings("unused")
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getEachNum() {
        return eachNum;
    }

    @SuppressWarnings("unused")
    public void setEachNum(Long eachNum) {
        this.eachNum = eachNum;
    }

    public String getExecUser() {
        return execUser;
    }

    @SuppressWarnings("unused")
    public void setExecUser(String execUser) {
        this.execUser = execUser;
    }

    public Integer getTaskScheduler() {
        return taskScheduler;
    }

    @SuppressWarnings("unused")
    public void setTaskScheduler(Integer taskScheduler) {
        this.taskScheduler = taskScheduler;
    }

    public Timestamp getTaskTime() {
        return taskTime;
    }

    @SuppressWarnings("unused")
    public void setTaskTime(Timestamp taskTime) {
        this.taskTime = taskTime;
    }

    public String getTaskCron() {
        return taskCron;
    }

    @SuppressWarnings("unused")
    public void setTaskCron(String taskCron) {
        this.taskCron = taskCron;
    }

    public String getPublishDesc() {
        return publishDesc;
    }

    @SuppressWarnings("unused")
    public void setPublishDesc(String publishDesc) {
        this.publishDesc = publishDesc;
    }

    public String getStartUser() {
        return startUser;
    }

    @SuppressWarnings("unused")
    public void setStartUser(String startUser) {
        this.startUser = startUser;
    }

    public Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(Timestamp startTime) {
        this.startTime = startTime;
    }

    public Long getTimeout() {
        return timeout;
    }

    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }

    public String getTaskTimeForDispaly() {
        return taskTimeForDispaly;
    }

    @SuppressWarnings("unused")
    public void setTaskTimeForDispaly(String taskTimeForDispaly) {
        this.taskTimeForDispaly = taskTimeForDispaly;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDriveMode() {
        return driveMode;
    }

    @SuppressWarnings("unused")
    public void setDriveMode(Integer driveMode) {
        this.driveMode = driveMode;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public Integer getReadyToExecute() {
        return readyToExecute;
    }

    @SuppressWarnings("unused")
    public void setReadyToExecute(Integer readyToExecute) {
        this.readyToExecute = readyToExecute;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Long getIscriptAuditRelationId() {
        return iscriptAuditRelationId;
    }

    @SuppressWarnings("unused")
    public void setIscriptAuditRelationId(Long iscriptAuditRelationId) {
        this.iscriptAuditRelationId = iscriptAuditRelationId;
    }

    public Long getScriptTaskId() {
        return scriptTaskId;
    }

    public void setScriptTaskId(Long scriptTaskId) {
        this.scriptTaskId = scriptTaskId;
    }

    public Long getApprWorkitemId() {
        return apprWorkitemId;
    }

    @SuppressWarnings("unused")
    public void setApprWorkitemId(Long apprWorkitemId) {
        this.apprWorkitemId = apprWorkitemId;
    }

    public Integer getAuditType() {
        return auditType;
    }

    @SuppressWarnings("unused")
    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Timestamp getApplyTime() {
        return applyTime;
    }

    @SuppressWarnings("unused")
    public void setApplyTime(Timestamp applyTime) {
        this.applyTime = applyTime;
    }

    public Timestamp getAuditTime() {
        return auditTime;
    }

    @SuppressWarnings("unused")
    public void setAuditTime(Timestamp auditTime) {
        this.auditTime = auditTime;
    }

    public String getAuditUser() {
        return auditUser;
    }

    @SuppressWarnings("unused")
    public void setAuditUser(String auditUser) {
        this.auditUser = auditUser;
    }

    public String getApplyUser() {
        return applyUser;
    }

    @SuppressWarnings("unused")
    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public Long getAuditUserId() {
        return auditUserId;
    }

    @SuppressWarnings("unused")
    public void setAuditUserId(Long auditUserId) {
        this.auditUserId = auditUserId;
    }

    public Long getApplyUserId() {
        return applyUserId;
    }

    @SuppressWarnings("unused")
    public void setApplyUserId(Long applyUserId) {
        this.applyUserId = applyUserId;
    }
    @Override
    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(Timestamp endTime) {
        this.endTime = endTime;
    }

    public Timestamp getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Timestamp currentTime) {
        this.currentTime = currentTime;
    }

    private String sysOrgCode;

    /**
     * 脚本分类绑定了部门筛选出来的分类路径列表
     */
    private List<String> orgCategoryPath;

    /**
     * 用户是否为管理员标识，true是，false不是，默认false
     */
    private Boolean superUser = false;

    public Boolean getSuperUser() {
        return superUser;
    }
    @Override
    public void setSuperUser(boolean superUser) {
        this.superUser = superUser;
    }

    public List<String> getOrgCategoryPath() {
        return orgCategoryPath;
    }
    @Override
    public void setOrgCategoryPath(List<String> orgCategoryPath) {
        this.orgCategoryPath = orgCategoryPath;
    }

    public String getSysOrgCode() {
        return sysOrgCode;
    }
    @Override
    public void setSysOrgCode(String sysOrgCode) {
        this.sysOrgCode = sysOrgCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("scriptTaskId", getScriptTaskId())
                .append("srcScriptUuid", getSrcScriptUuid())
                .append("taskName", getTaskName())
                .append("eachNum", getEachNum())
                .append("execUser", getExecUser())
                .append("taskScheduler", getTaskScheduler())
                .append("taskTime", getTaskTime())
                .append("taskCron", getTaskCron())
                .append("publishDesc", getPublishDesc())
                .append("startUser", getStartUser())
                .append("startTime", getStartTime())
                .append("timeout", getTimeout())
                .append("taskTimeForDispaly", getTaskTimeForDispaly())
                .append("type", getType())
                .append("driveMode", getDriveMode())
                .append("startType", getStartType())
                .append("readyToExecute", getReadyToExecute())
                .append("creatorId", getCreatorId())
                .append("creatorName", getCreatorName())
                .append("updatorId", getUpdatorId())
                .append("updatorName", getUpdatorName())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("iscriptAuditRelationId", getIscriptAuditRelationId())
                .append("apprWorkitemId", getApprWorkitemId())
                .append("auditType", getAuditType())
                .append("state", getState())
                .append("applyTime", getApplyTime())
                .append("auditTime", getAuditTime())
                .append("auditUser", getAuditUser())
                .append("applyUser", getApplyUser())
                .append("auditUserId", getAuditUserId())
                .append("applyUserId", getApplyUserId())
                .append("scriptCategoryName", getScriptCategoryName())
                .append("scriptTaskInstanceId", getScriptTaskInstanceId())
                .append("scriptTaskInstanceState", getScriptTaskInstanceState())
                .append("runAgentCount", getRunAgentCount())
                .append("version", getVersion())
                .append("elapsedTime", getElapsedTime())
                .append("endTime", getEndTime())
                .append("currentTime", getCurrentTime())
                .append("sysOrgCode", getSysOrgCode())
                .append("orgCategoryPath", getOrgCategoryPath())
                .append("superUser", getSuperUser())
                .toString();
    }
}
