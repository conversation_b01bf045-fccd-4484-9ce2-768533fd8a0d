package com.ideal.script.service.impl.resulthandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.constants.StrPool;
import com.ideal.sc.util.Base64;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.constant.enums.JsonKey;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.TaskHandleParam;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.service.IAfterRuntimeHandlerService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.resulthandler.IScriptResultHandlerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 脚本执行结果处理类
 *
 * <AUTHOR>
 */
@Service
public class ScriptResultHandlerServiceImpl implements IScriptResultHandlerService {
    private final Logger logger = LoggerFactory.getLogger(ScriptResultHandlerServiceImpl.class);

    private final RedissonClient redissonClient;

    private final ITaskRuntimeService taskRuntimeService;

    private final ITaskInstanceService taskInstanceService;

    private final ObjectMapper objectMapper;

    public ScriptResultHandlerServiceImpl(RedissonClient redissonClient, ITaskRuntimeService taskRuntimeService, ITaskInstanceService taskInstanceService, ObjectMapper objectMapper) {
        this.redissonClient = redissonClient;
        this.taskRuntimeService = taskRuntimeService;
        this.taskInstanceService = taskInstanceService;
        this.objectMapper = objectMapper;
    }

    /**
     * 处理脚本执行结果 This method processes the result of script execution.
     *
     * @param messageList 包含执行结果信息的JSON消息
     */
    @Override
    public void handleScriptExecuteResult(List<String> messageList) throws ScriptException {
        try {
            if (CollectionUtils.isNotEmpty(messageList)) {
                for (String message : messageList) {
                    if(logger.isDebugEnabled()) {
                        logger.debug("the result from the script-execute-result topic returned successfully,The received message is:{}", message);
                    }
                    validateScriptResultJson(message);

                    // 遍历每个JSON对象并进行转换
                    //记录是否更新instance成功 驱动下一个批次方法需要该值判断，如果等于1，才驱动
                    JsonNode obj = objectMapper.readTree(message);
                    String taskRuntimeId = obj.get("scriptId").asText();
                    //获取agent实例数据
                    TaskRuntimeDto taskRuntimeDto = taskRuntimeService.selectTaskRuntimeById(Long.parseLong(taskRuntimeId));
                    String bizId = taskRuntimeDto.getBizId();
                    if (StringUtils.isBlank(bizId)) {
                        logger.error("handleScriptExecuteResult bizId is blank!");
                        throw new ScriptException("handleScriptExecuteResult bizId is blank!");
                    }
                    //校验是否多次消费
                    if(hasConsumed(Constants.CHECK_CONSUMPTION_TOPIC_EXEC + taskRuntimeDto.getBizId())){
                        logger.warn("script-execute-result topic,The taskRuntimeId {} has been processed, skipping.", taskRuntimeId);
                        return;
                    }
                    String stdout = Base64.getFromBase64(obj.get("stdout").asText());
                    String stderr = Base64.getFromBase64(obj.get("stderr").asText());
                    String lastLine = Base64.getFromBase64(obj.get("lastLine").asText());
                    // 判断bizId是否是
                    int status = obj.get("status").asInt();
                    TaskHandleParam taskHandleParam = new TaskHandleParam();
                    taskHandleParam.setIstdout(stdout);
                    taskHandleParam.setIstderror(stderr);
                    taskHandleParam.setIlastline(lastLine);
                    //TODO 终止正常进入不到这里，需要改调用终止方法那里改成同步sendSync方法
                    if (bizId.startsWith("agent-script-kill")) {
                        taskRuntimeService.handleScriptExecuteResult(taskRuntimeId, Enums.TaskRuntimeState.TERMINATED.getValue(), taskHandleParam);
                    } else {
                        taskRuntimeService.handleScriptExecuteResult(taskRuntimeId, status, taskHandleParam);
                    }
                }
            }
        } catch (ScriptException | JsonProcessingException e) {
            logger.error("handleScriptExecuteResult error:", e);
            throw new ScriptException("handle.script.execute.result.error");
        }
    }

    /**
     * 判断是否重复消费
     * @param bizId runtime表BizId
     * @return 返回true代表已经消费过，false代表没有消费过
     */
    private boolean hasConsumed(String bizId){
        RBucket<String> bucket = redissonClient.getBucket(Constants.CHECK_CONSUMPTION_SCRIPT_RUNTIME + bizId);
        return !bucket.setIfAbsent("value", Duration.ofMinutes(1));
    }

    /**
     * 检查给定的JSON字符串中是否包含指定的关键字，如果缺少任何一个关键字，方法就会抛出一个ScriptException异常
     *
     * @param jsonMessage 包含执行结果信息的JSON消息
     */

    public void validateScriptResultJson(String jsonMessage) throws ScriptException {
        try {
            JsonNode innerNode = objectMapper.readTree(jsonMessage);
            // 验证关键属性
            if (!innerNode.has(JsonKey.SCRIPT_ID.getKey())
                    || !innerNode.has(JsonKey.STDOUT.getKey())
                    || !innerNode.has(JsonKey.LAST_LINE.getKey())
                    || !innerNode.has(JsonKey.STDERR.getKey())
                    || !innerNode.has(JsonKey.TRANSCODING.getKey())
                    || !innerNode.has(JsonKey.STATUS.getKey())) {
                logger.error("The JSON returned from the script execution result is missing a key property!");
                throw new ScriptException("error.json.missing.key");
            }
        } catch (Exception e) {
            logger.error("Exception occurred while parsing the JSON returned from the script execution result.");
            throw new ScriptException("error.json.parsing.exception");
        }
    }

    /**
     * 管理服务发送agent执行后返回的调用结果
     *
     * @param message 管理服务发送agent执行后返回的调用结果
     */
    @Override
    @SuppressWarnings({"java:S2222"})
    public void handleScriptSendResult(String message) throws ScriptException {
        if(logger.isDebugEnabled()) {
            logger.debug("the result from the script-send-result topic returned successfully,The received message is:{}", message);
        }
        try {
            // 存储管理服务提供的taskId，（管理服务的ieai_agent_task_send_result表中的itask_id）
            validateScriptSendResultJson(message);
            // json解析
            JsonNode jsonNode = objectMapper.readTree(message);

            String bizId = Optional.of(jsonNode.get("bizId")).get().asText();
            //校验是否多次消费
            if(hasConsumed(Constants.CHECK_CONSUMPTION_TOPIC_SEND + bizId)){
                logger.warn("script-send-result topic,The bizId {} has been processed, skipping.", bizId);
                return;
            }
            // 提取taskRuntimeId
            String taskRuntimeId = "";
            if (bizId != null && bizId.contains(Enums.Separator.DASH.getValue())) {
                String[] parts = bizId.split(Enums.Separator.DASH.getValue());
                taskRuntimeId = parts[parts.length - 1];
            }
            String agentIp = jsonNode.get("agentIp").asText();
            int agentPort = jsonNode.get("agentPort").asInt();
            long taskId = jsonNode.get("taskId").asLong();

            if (taskRuntimeId.isEmpty()) {
                logger.error("method handleScriptSendResult , the taskRuntimeId extracted from bizId is empty.message:{}",message);
                throw new ScriptException("error.handle.script.send.result.taskRuntimeId.empty");
            }


            TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
            taskRuntimeDto.setAgentTaskId(taskId);
            taskRuntimeDto.setId(Long.parseLong(taskRuntimeId));

            //定时任务启动的脚本任务发送正常推送mq，如果工具箱、脚本任务有什么特殊处理再加对应的接口实现类
            taskRuntimeDto.setAgentIp(agentIp);
            taskRuntimeDto.setAgentPort(agentPort);
            TaskInstanceDto taskInstanceByRuntimeId = taskInstanceService.getTaskInstanceByRuntimeId(Long.parseLong(taskRuntimeId));
            if(Enums.ScriptStartType.TIMETASK.getStartValue().equals(taskInstanceByRuntimeId.getStartType().toString())){
                IAfterRuntimeHandlerService bean = SpringUtil.getBean(Enums.ScriptStartType.getValueByStartValue(taskInstanceByRuntimeId.getStartType().toString()));
                bean.monitorMqToSend(Constants.CRONTABS_SEND_RESULT_DEV,null,taskRuntimeDto);
            }


        } catch (JsonProcessingException e) {
            throw new ScriptException("JsonProcessingException : "+message, e);
        } catch (Exception e) {
            logger.error("An Exception occurred during the execution of the handleScriptSendResult method,message:{}",message, e);
            throw new ScriptException("error.handle.script.send.result");
        }

    }

    public void validateScriptSendResultJson(String jsonData) throws ScriptException {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonData);

            boolean containsAllKeys = Stream.of("bizId", "agentIp", "agentPort", "taskId", "send")
                                            .allMatch(jsonNode::has);

            if (!containsAllKeys) {
                logger.error("method validateScriptSendResultJson ,The JSON returned from the script send result is missing a key property!");
                throw new ScriptException("error.json.missing.key");
            }
        } catch (Exception e) {
            logger.error("validateScriptSendResultJson error:", e);
            throw new ScriptException("error.json.parsing.exception");
        }
    }



    public void bizIdValid(String bizId) throws ScriptException {
        if (bizId.isEmpty()) {
            logger.error("The taskRuntimeId extracted from bizId is empty.");
            throw new ScriptException("error.handle.script.send.result.taskRuntimeId.empty");
        }
    }

    /**
     * 管理服务发送异常返回的调用结果（script-error-result）
     *
     * @param message 管理服务发送异常返回的调用结果（script-error-result）
     */
    @Override
    @SuppressWarnings({"java:S2222"})
    public void handleScriptErrorResult(String message, String bizId, Long agentTaskId) throws ScriptException {
        logger.info("handleScriptErrorResult bizId: {},  taskId: {},taskRuntimeId:{}", bizId, agentTaskId, bizId);
        bizIdValid(bizId);
        try {
            //校验是否多次消费
            if (hasConsumed(Constants.CHECK_CONSUMPTION_TOPIC_ERROR + bizId)) {
                logger.warn("script-error-result topic,The taskRuntimeId {} has been processed, skipping.", bizId);
                return;
            }
            processScriptError(message, bizId);
        } catch (Exception e) {
            logger.error("An Exception occurred during the script-error-result topic of the handleScriptErrorResult method.", e);
            throw new ScriptException("fail.handle.script.error.result",e);
        }
    }

    private void processScriptError(String message, String bizId) throws ScriptException {
        if (StringUtils.isBlank(message)) {
            logger.warn("processScriptTaskErrorTopic message is empty, bizId: {}", bizId);
        }
        // agent-script-start-from-exec-1108604370107645954 截取最后一个-后的id
        String taskRuntimeId = StringUtils.substringAfterLast(bizId, StrPool.DASHED);
        TaskHandleParam taskHandleParam = new TaskHandleParam();
        taskHandleParam.setIstdout("");
        taskHandleParam.setIstderror(message);
        taskHandleParam.setIlastline("");
        taskRuntimeService.handleScriptExecuteResult(taskRuntimeId, Enums.TaskRuntimeState.SCRIPT_FAIL_STATE.getValue(), taskHandleParam);
    }


}
