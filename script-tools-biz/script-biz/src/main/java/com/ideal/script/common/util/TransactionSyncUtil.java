package com.ideal.script.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Objects;

/**
 * Spring事务工具类
 */
public class TransactionSyncUtil {
    private static final Logger logger = LoggerFactory.getLogger(TransactionSyncUtil.class);

    /**
     * 在事务完成后执行指定的业务逻辑（支持提交、回滚和未知状态）
     *
     * @param onCommit   事务提交后需要执行的业务逻辑
     * @param onRollback 事务回滚后需要执行的业务逻辑
     * @param onUnknown  事务状态未知时需要执行的业务逻辑
     */
    public static <T, E extends Exception> void execute(ThrowingConsumer<T, E> onCommit, ThrowingConsumer<T, E> onRollback, ThrowingConsumer<T, E> onUnknown, T input) {
        if (!TransactionSynchronizationManager.isSynchronizationActive()) {
            throw new IllegalStateException("Transaction synchronization is not active");
        }
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCompletion(int status) {
                switch (status) {
                    case STATUS_COMMITTED:
                        try {
                            if (Objects.nonNull(onCommit)) {
                                onCommit.accept(input); // 事务提交后执行
                            }
                        } catch (Exception e) {
                            logger.error("Error executing task after transaction commit", e);
                        }
                        break;
                    case STATUS_ROLLED_BACK:
                        try {
                            if (Objects.nonNull(onRollback)) {
                                onRollback.accept(input); // 事务回滚后执行
                            }
                        } catch (Exception e) {
                            logger.error("Error executing task after transaction rollback", e);
                        }
                        break;
                    case STATUS_UNKNOWN:
                        try {
                            if (Objects.nonNull(onUnknown)) {
                                onUnknown.accept(input); // 事务状态未知时执行
                            }
                        } catch (Exception e) {
                            logger.error("Error executing task after transaction unknown status", e);
                        }
                        break;
                }
            }
        });
    }
}