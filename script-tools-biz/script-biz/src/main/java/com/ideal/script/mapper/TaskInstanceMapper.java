package com.ideal.script.mapper;


import com.ideal.script.model.entity.TaskInstance;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 脚本任务运行实例Mapper接口
 *
 * <AUTHOR>
 */
public interface TaskInstanceMapper
{
    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
     TaskInstance selectTaskInstanceById(Long id);

    /**
     * 查询任务实例的总数
     * @param id instanceId
     * @return 查询任务实例的总数
     */
    Long selectIpsCount(Long id);

    /**
     * 根据id查询instance实例
     * @param ids instanceId集合
     * @return instanceId集合
     */
     List<Long> selectTaskInstanceIdsById(@Param("ids")Long[] ids);

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    TaskInstance selectTaskInstanceByTaskId(Long id);


    /**
     * 通过taskInfoId查询instance信息
     * @param taskInfoId 任务id
     * @return 任务实例
     */
    TaskInstance getTaskInstanceByTaskInfoId(Long taskInfoId);

    /**
     * 获取任务实例对象
     * @param runtimeId agent实例id
     * @return 任务实例对象
     */
    TaskInstance getTaskInstanceByRuntimeId(Long runtimeId);

    /**
     * 查询脚本任务运行实例列表
     *
     * @param taskInstance 脚本任务运行实例
     * @return 脚本任务运行实例集合
     */
     List<TaskInstance> selectTaskInstanceList(TaskInstance taskInstance);

    /**
     * 新增脚本任务运行实例
     *
     * @param taskInstance 脚本任务运行实例
     * @return 结果
     */
     int insertTaskInstance(TaskInstance taskInstance);

    /**
     * 修改脚本任务运行实例
     *
     * @param taskInstance 脚本任务运行实例
     * @return 结果
     */
     int updateTaskInstance(TaskInstance taskInstance);

    /**
     * 删除脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 结果
     */
     int deleteTaskInstanceById(Long id);

    /**
     * 批量删除脚本任务运行实例
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deleteTaskInstanceByIds(@Param("ids")Long[] ids);


    /**
     * 更新server数量
     *
     * @param taskInstanceId 任务实例ID
     * @param notInStates  不包括的agent运行实例状态
     */
    void updateServerNum(@Param("taskInstanceId") Long taskInstanceId,  @Param("notInStates") List<Integer> notInStates);


    /**
     * 任务中agent运行实例状态的统计
     *
     * @param taskInstanceId 任务实例id
     * @return list
     */
    List<Map<String,Object>> getStatusSummary(Long taskInstanceId);

    /**
     * 更新任务状态，设置为完成或者失败
     *
     * @param state 状态
     * @param taskInstanceId 实例任务ID
     * @param excludeStates 排除状态
     */
    int updateState(@Param("state")int state, @Param("taskInstanceId")Long taskInstanceId,  @Param("excludeStates")int[] excludeStates);

    /**
     * 批量更新任务状态，设置为完成或者失败
     *
     * @param state 状态
     * @param taskInstanceId 实例任务ID集合
     * @param excludeStates 排除状态
     */
    int updateBatchTaskState(@Param("state")int state, @Param("taskInstanceId")Long [] taskInstanceId,  @Param("excludeStates")int[] excludeStates);

    /**
     * 更新正在运行的设备数
     *
     * @param taskInstanceId 实例任务ID
     */
    void updateRunNum(@Param("taskInstanceId") Long taskInstanceId);


    /**
     * 功能描述: 更新脚本任务实例表结束时间
     *
     * @param taskInstanceId 脚本实例表主键
     * @param notInStates    不包含分批执行的状态
     * <AUTHOR>
     */
    void updateEndTime(@Param("taskInstanceId")Long taskInstanceId, @Param("notInStates")List<Integer> notInStates);

    /**
     * 功能描述： 根据任务总表id，获取这个任务下所有运行中的实例id集合
     * @param scriptTaskId 任务id
     * @return 实例id集合
     */
    List<Long> selectRunningTaskByScriptTaskId(@Param("scriptTaskId")Long scriptTaskId);
    /**
     * 查询脚本任务运行实例集合
     *
     * @param taskInstanceIds 脚本任务运行实例主键
     * @return 脚本任务运行实例集合
     */
    List<TaskInstance> selectTaskInstanceByIds(@Param("taskInstanceIds") Long[] taskInstanceIds);
}
