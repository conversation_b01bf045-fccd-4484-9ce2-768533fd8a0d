package com.ideal.script.model.entity;

import com.ideal.snowflake.annotion.IdGenerator;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * itsm投产脚本
 *
 * <AUTHOR>
 * {@code @date} 2025-06-16
 */
public class ItsmProductAttachment implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @IdGenerator
    @Min(value = 0)
    private Long iid;

    /**
     * 表ieai_script_itsm_publish_info主键
     */
    private Long publishInfoId;
    /**
     * 脚本uuid
     */
    private String srcScriptUuid;
    /**
     * 表ieai_script_itsm_product_info主键
     */
    private Long itsmProductInfoId;
    /**
     * 附件内容
     */
    private byte[] attachmentContent;

    /**
     * 附件大小
     */
    private Long attachmentSize;

    /**
     * 附件名称
     */
    private String attachmentName;

    public Long getIid() {
        return iid;
    }

    public void setIid(Long iid) {
        this.iid = iid;
    }

    public Long getPublishInfoId() {
        return publishInfoId;
    }

    public void setPublishInfoId(Long publishInfoId) {
        this.publishInfoId = publishInfoId;
    }

    public String getSrcScriptUuid() {
        return srcScriptUuid;
    }

    public void setSrcScriptUuid(String srcScriptUuid) {
        this.srcScriptUuid = srcScriptUuid;
    }

    public Long getItsmProductInfoId() {
        return itsmProductInfoId;
    }

    public void setItsmProductInfoId(Long itsmProductInfoId) {
        this.itsmProductInfoId = itsmProductInfoId;
    }

    public byte[] getAttachmentContent() {
        return attachmentContent;
    }

    public void setAttachmentContent(byte[] attachmentContent) {
        this.attachmentContent = attachmentContent;
    }

    public Long getAttachmentSize() {
        return attachmentSize;
    }

    public void setAttachmentSize(Long attachmentSize) {
        this.attachmentSize = attachmentSize;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }
}

